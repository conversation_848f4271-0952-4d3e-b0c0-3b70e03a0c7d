import { create } from 'zustand';
import type {
  TextPromptModule,
  ImagePromptModule,
  AIModelConfig,
  WorkflowTemplate,
  Category,
  ParameterConfig
} from '../types';

interface AppState {
  // 模块库数据
  textPrompts: TextPromptModule[];
  imagePrompts: ImagePromptModule[];
  categories: Category[];
  parameters: ParameterConfig[];
  
  // AI模型配置
  aiModels: AIModelConfig[];
  
  // 工作流模板
  templates: WorkflowTemplate[];
  
  // 当前工作流状态
  currentWorkflow: {
    nodes: any[];
    edges: any[];
    viewport: { x: number; y: number; zoom: number };
  };
  
  // UI状态
  selectedNodeId: string | null;
  sidebarCollapsed: boolean;
  activeTab: 'text' | 'image' | 'param';
  
  // Actions
  setTextPrompts: (prompts: TextPromptModule[]) => void;
  addTextPrompt: (prompt: TextPromptModule) => void;
  updateTextPrompt: (id: string, prompt: Partial<TextPromptModule>) => void;
  deleteTextPrompt: (id: string) => void;
  
  setImagePrompts: (prompts: ImagePromptModule[]) => void;
  addImagePrompt: (prompt: ImagePromptModule) => void;
  updateImagePrompt: (id: string, prompt: Partial<ImagePromptModule>) => void;
  deleteImagePrompt: (id: string) => void;
  
  setCategories: (categories: Category[]) => void;
  addCategory: (category: Category) => void;
  updateCategory: (id: string, category: Partial<Category>) => void;
  deleteCategory: (id: string) => void;
  
  setParameters: (parameters: ParameterConfig[]) => void;
  
  setAIModels: (models: AIModelConfig[]) => void;
  addAIModel: (model: AIModelConfig) => void;
  updateAIModel: (id: string, model: Partial<AIModelConfig>) => void;
  deleteAIModel: (id: string) => void;
  
  setTemplates: (templates: WorkflowTemplate[]) => void;
  addTemplate: (template: WorkflowTemplate) => void;
  deleteTemplate: (id: string) => void;
  
  setCurrentWorkflow: (workflow: any) => void;
  updateWorkflowNodes: (nodes: any[]) => void;
  updateWorkflowEdges: (edges: any[]) => void;
  updateWorkflowViewport: (viewport: { x: number; y: number; zoom: number }) => void;
  
  setSelectedNodeId: (id: string | null) => void;
  setSidebarCollapsed: (collapsed: boolean) => void;
  setActiveTab: (tab: 'text' | 'image' | 'param') => void;
}

export const useAppStore = create<AppState>((set, get) => ({
  // 初始状态
  textPrompts: [],
  imagePrompts: [],
  categories: [],
  parameters: [],
  aiModels: [],
  templates: [],
  currentWorkflow: {
    nodes: [],
    edges: [],
    viewport: { x: 0, y: 0, zoom: 1 }
  },
  selectedNodeId: null,
  sidebarCollapsed: false,
  activeTab: 'text',
  
  // Actions
  setTextPrompts: (prompts) => set({ textPrompts: prompts }),
  addTextPrompt: (prompt) => set((state) => ({ 
    textPrompts: [...state.textPrompts, prompt] 
  })),
  updateTextPrompt: (id, prompt) => set((state) => ({
    textPrompts: state.textPrompts.map(p => p.id === id ? { ...p, ...prompt } : p)
  })),
  deleteTextPrompt: (id) => set((state) => ({
    textPrompts: state.textPrompts.filter(p => p.id !== id)
  })),
  
  setImagePrompts: (prompts) => set({ imagePrompts: prompts }),
  addImagePrompt: (prompt) => set((state) => ({ 
    imagePrompts: [...state.imagePrompts, prompt] 
  })),
  updateImagePrompt: (id, prompt) => set((state) => ({
    imagePrompts: state.imagePrompts.map(p => p.id === id ? { ...p, ...prompt } : p)
  })),
  deleteImagePrompt: (id) => set((state) => ({
    imagePrompts: state.imagePrompts.filter(p => p.id !== id)
  })),
  
  setCategories: (categories) => set({ categories }),
  addCategory: (category) => set((state) => ({ 
    categories: [...state.categories, category] 
  })),
  updateCategory: (id, category) => set((state) => ({
    categories: state.categories.map(c => c.id === id ? { ...c, ...category } : c)
  })),
  deleteCategory: (id) => set((state) => ({
    categories: state.categories.filter(c => c.id !== id)
  })),
  
  setParameters: (parameters) => set({ parameters }),
  
  setAIModels: (models) => set({ aiModels: models }),
  addAIModel: (model) => set((state) => ({ 
    aiModels: [...state.aiModels, model] 
  })),
  updateAIModel: (id, model) => set((state) => ({
    aiModels: state.aiModels.map(m => m.id === id ? { ...m, ...model } : m)
  })),
  deleteAIModel: (id) => set((state) => ({
    aiModels: state.aiModels.filter(m => m.id !== id)
  })),
  
  setTemplates: (templates) => set({ templates }),
  addTemplate: (template) => set((state) => ({ 
    templates: [...state.templates, template] 
  })),
  deleteTemplate: (id) => set((state) => ({
    templates: state.templates.filter(t => t.id !== id)
  })),
  
  setCurrentWorkflow: (workflow) => set({ currentWorkflow: workflow }),
  updateWorkflowNodes: (nodes) => set((state) => ({
    currentWorkflow: { ...state.currentWorkflow, nodes }
  })),
  updateWorkflowEdges: (edges) => set((state) => ({
    currentWorkflow: { ...state.currentWorkflow, edges }
  })),
  updateWorkflowViewport: (viewport) => set((state) => ({
    currentWorkflow: { ...state.currentWorkflow, viewport }
  })),
  
  setSelectedNodeId: (id) => set({ selectedNodeId: id }),
  setSidebarCollapsed: (collapsed) => set({ sidebarCollapsed: collapsed }),
  setActiveTab: (tab) => set({ activeTab: tab }),
}));
