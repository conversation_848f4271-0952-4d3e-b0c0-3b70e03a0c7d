import { create } from 'zustand';
import type {
  TextPromptModule,
  ImagePromptModule,
  AIModelConfig,
  WorkflowTemplate,
  Category,
  ParameterConfig
} from '../types';

interface AppState {
  // 模块库数据
  textPrompts: TextPromptModule[];
  imagePrompts: ImagePromptModule[];
  categories: Category[];
  parameters: ParameterConfig[];
  
  // AI模型配置
  aiModels: AIModelConfig[];
  
  // 工作流模板
  templates: WorkflowTemplate[];
  
  // 当前工作流状态
  currentWorkflow: {
    nodes: any[];
    edges: any[];
    viewport: { x: number; y: number; zoom: number };
  };
  
  // UI状态
  selectedNodeId: string | null;
  sidebarCollapsed: boolean;
  activeTab: 'text' | 'image' | 'param';
  
  // Actions
  setTextPrompts: (prompts: TextPromptModule[]) => void;
  addTextPrompt: (prompt: TextPromptModule) => void;
  updateTextPrompt: (id: string, prompt: Partial<TextPromptModule>) => void;
  deleteTextPrompt: (id: string) => void;
  
  setImagePrompts: (prompts: ImagePromptModule[]) => void;
  addImagePrompt: (prompt: ImagePromptModule) => void;
  updateImagePrompt: (id: string, prompt: Partial<ImagePromptModule>) => void;
  deleteImagePrompt: (id: string) => void;
  
  setCategories: (categories: Category[]) => void;
  addCategory: (category: Category) => void;
  updateCategory: (id: string, category: Partial<Category>) => void;
  deleteCategory: (id: string) => void;
  
  setParameters: (parameters: ParameterConfig[]) => void;
  
  setAIModels: (models: AIModelConfig[]) => void;
  addAIModel: (model: AIModelConfig) => void;
  updateAIModel: (id: string, model: Partial<AIModelConfig>) => void;
  deleteAIModel: (id: string) => void;
  
  setTemplates: (templates: WorkflowTemplate[]) => void;
  addTemplate: (template: WorkflowTemplate) => void;
  deleteTemplate: (id: string) => void;
  
  setCurrentWorkflow: (workflow: any) => void;
  updateWorkflowNodes: (nodes: any[]) => void;
  updateWorkflowEdges: (edges: any[]) => void;
  updateWorkflowViewport: (viewport: { x: number; y: number; zoom: number }) => void;
  
  setSelectedNodeId: (id: string | null) => void;
  setSidebarCollapsed: (collapsed: boolean) => void;
  setActiveTab: (tab: 'text' | 'image' | 'param') => void;
}

export const useAppStore = create<AppState>((set, get) => ({
  // 初始状态
  textPrompts: [
    {
      id: 'text-1',
      name: '基础描述',
      content: '一个美丽的风景',
      category: 'basic',
      tags: ['风景', '自然']
    },
    {
      id: 'text-2',
      name: '风格描述',
      content: '油画风格，印象派',
      category: 'style',
      tags: ['油画', '印象派']
    }
  ],
  imagePrompts: [
    {
      id: 'img-1',
      name: '参考图片',
      url: 'https://via.placeholder.com/300x200',
      description: '示例参考图片',
      category: 'reference',
      tags: ['参考']
    }
  ],
  categories: [
    { id: 'basic', name: '基础描述', color: '#2196F3' },
    { id: 'style', name: '风格', color: '#4CAF50' },
    { id: 'reference', name: '参考', color: '#FF9800' }
  ],
  parameters: [
    {
      id: 'param-1',
      name: '图片质量',
      type: 'slider',
      defaultValue: 0.8,
      min: 0,
      max: 1,
      step: 0.1
    }
  ],
  aiModels: [
    {
      id: 'model-1',
      name: 'Stable Diffusion XL',
      type: 'Stable Diffusion',
      api_endpoint: 'https://api.example.com/sdxl',
      api_key: ''
    }
  ],
  templates: [],
  currentWorkflow: {
    nodes: [
      {
        id: 'text-node-1',
        type: 'textNode',
        position: { x: 100, y: 100 },
        data: {
          content: '一个美丽的风景',
          label: '基础描述'
        }
      },
      {
        id: 'text-node-2',
        type: 'textNode',
        position: { x: 100, y: 250 },
        data: {
          content: '油画风格，印象派',
          label: '风格描述'
        }
      },
      {
        id: 'output-node-1',
        type: 'outputNode',
        position: { x: 400, y: 175 },
        data: {
          label: '最终输出'
        }
      }
    ],
    edges: [
      {
        id: 'edge-1',
        source: 'text-node-1',
        target: 'output-node-1',
        sourceHandle: 'output',
        targetHandle: 'input'
      },
      {
        id: 'edge-2',
        source: 'text-node-2',
        target: 'output-node-1',
        sourceHandle: 'output',
        targetHandle: 'input'
      }
    ],
    viewport: { x: 0, y: 0, zoom: 1 }
  },
  selectedNodeId: null,
  sidebarCollapsed: false,
  activeTab: 'text',
  
  // Actions
  setTextPrompts: (prompts) => set({ textPrompts: prompts }),
  addTextPrompt: (prompt) => set((state) => ({ 
    textPrompts: [...state.textPrompts, prompt] 
  })),
  updateTextPrompt: (id, prompt) => set((state) => ({
    textPrompts: state.textPrompts.map(p => p.id === id ? { ...p, ...prompt } : p)
  })),
  deleteTextPrompt: (id) => set((state) => ({
    textPrompts: state.textPrompts.filter(p => p.id !== id)
  })),
  
  setImagePrompts: (prompts) => set({ imagePrompts: prompts }),
  addImagePrompt: (prompt) => set((state) => ({ 
    imagePrompts: [...state.imagePrompts, prompt] 
  })),
  updateImagePrompt: (id, prompt) => set((state) => ({
    imagePrompts: state.imagePrompts.map(p => p.id === id ? { ...p, ...prompt } : p)
  })),
  deleteImagePrompt: (id) => set((state) => ({
    imagePrompts: state.imagePrompts.filter(p => p.id !== id)
  })),
  
  setCategories: (categories) => set({ categories }),
  addCategory: (category) => set((state) => ({ 
    categories: [...state.categories, category] 
  })),
  updateCategory: (id, category) => set((state) => ({
    categories: state.categories.map(c => c.id === id ? { ...c, ...category } : c)
  })),
  deleteCategory: (id) => set((state) => ({
    categories: state.categories.filter(c => c.id !== id)
  })),
  
  setParameters: (parameters) => set({ parameters }),
  
  setAIModels: (models) => set({ aiModels: models }),
  addAIModel: (model) => set((state) => ({ 
    aiModels: [...state.aiModels, model] 
  })),
  updateAIModel: (id, model) => set((state) => ({
    aiModels: state.aiModels.map(m => m.id === id ? { ...m, ...model } : m)
  })),
  deleteAIModel: (id) => set((state) => ({
    aiModels: state.aiModels.filter(m => m.id !== id)
  })),
  
  setTemplates: (templates) => set({ templates }),
  addTemplate: (template) => set((state) => ({ 
    templates: [...state.templates, template] 
  })),
  deleteTemplate: (id) => set((state) => ({
    templates: state.templates.filter(t => t.id !== id)
  })),
  
  setCurrentWorkflow: (workflow) => set({ currentWorkflow: workflow }),
  updateWorkflowNodes: (nodes) => set((state) => ({
    currentWorkflow: { ...state.currentWorkflow, nodes }
  })),
  updateWorkflowEdges: (edges) => set((state) => ({
    currentWorkflow: { ...state.currentWorkflow, edges }
  })),
  updateWorkflowViewport: (viewport) => set((state) => ({
    currentWorkflow: { ...state.currentWorkflow, viewport }
  })),
  
  setSelectedNodeId: (id) => set({ selectedNodeId: id }),
  setSidebarCollapsed: (collapsed) => set({ sidebarCollapsed: collapsed }),
  setActiveTab: (tab) => set({ activeTab: tab }),
}));
