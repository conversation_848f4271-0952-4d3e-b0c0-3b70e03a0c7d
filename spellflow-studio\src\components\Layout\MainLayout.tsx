import React from 'react';
import { Box, AppBar, Toolbar, Typography, IconButton } from '@mui/material';
import { Menu as MenuIcon, Settings as SettingsIcon, Save as SaveIcon, FolderOpen as FolderOpenIcon } from '@mui/icons-material';
import { useAppStore } from '../../store';
import Sidebar from '../Sidebar/Sidebar';
import Canvas from '../Canvas/Canvas';

const MainLayout: React.FC = () => {
  const { sidebarCollapsed, setSidebarCollapsed } = useAppStore();

  const handleToggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  return (
    <Box sx={{ display: 'flex', height: '100vh', overflow: 'hidden' }}>
      {/* 顶部工具栏 */}
      <AppBar 
        position="fixed" 
        sx={{ 
          zIndex: (theme) => theme.zIndex.drawer + 1,
          backgroundColor: '#1976d2'
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="toggle sidebar"
            onClick={handleToggleSidebar}
            edge="start"
            sx={{ mr: 2 }}
          >
            <MenuIcon />
          </IconButton>
          
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            咒语流工作室 (SpellFlow Studio)
          </Typography>
          
          {/* 工具栏按钮 */}
          <IconButton color="inherit" title="加载工作流">
            <FolderOpenIcon />
          </IconButton>
          
          <IconButton color="inherit" title="另存为模板">
            <SaveIcon />
          </IconButton>
          
          <IconButton color="inherit" title="AI模型设置">
            <SettingsIcon />
          </IconButton>
        </Toolbar>
      </AppBar>

      {/* 左侧边栏 */}
      <Sidebar />

      {/* 主内容区域 */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          height: '100vh',
          overflow: 'hidden',
          marginLeft: sidebarCollapsed ? 0 : '320px',
          transition: (theme) => theme.transitions.create('margin', {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.leavingScreen,
          }),
          marginTop: '64px', // AppBar height
        }}
      >
        <Canvas />
      </Box>
    </Box>
  );
};

export default MainLayout;
