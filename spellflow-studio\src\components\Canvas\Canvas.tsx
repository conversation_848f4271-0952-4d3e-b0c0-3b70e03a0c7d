import React, { useCallback, useRef, useEffect, useMemo } from 'react';
import {
  ReactFlow,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  addEdge,
  ReactFlowProvider,
  type Connection,
  type Edge,
  type Node,
} from 'reactflow';
import 'reactflow/dist/style.css';
import { Box } from '@mui/material';
import { useAppStore } from '../../store';
import type { NodeData } from '../../types';

// 导入自定义节点类型
import TextNode from '../Nodes/TextNode';
import ImageNode from '../Nodes/ImageNode';
import ParamNode from '../Nodes/ParamNode';
import OutputNode from '../Nodes/OutputNode';

// 定义节点类型映射 - 移到组件外部以避免重新创建
const nodeTypes = {
  textNode: TextNode,
  imageNode: ImageNode,
  paramNode: ParamNode,
  outputNode: OutputNode,
} as const;

const Canvas: React.FC = () => {
  const { 
    currentWorkflow, 
    updateWorkflowNodes, 
    updateWorkflowEdges,
    updateWorkflowViewport,
    setSelectedNodeId 
  } = useAppStore();

  // 使用 useMemo 确保 nodeTypes 对象稳定
  const memoizedNodeTypes = useMemo(() => nodeTypes, []);

  const [nodes, setNodes, onNodesChange] = useNodesState(currentWorkflow.nodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(currentWorkflow.edges);
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const reactFlowInstance = useRef<any>(null);

  // 处理连接
  const onConnect = useCallback(
    (params: Connection | Edge) => {
      const newEdge = addEdge(params, edges);
      setEdges(newEdge);
      updateWorkflowEdges(newEdge);
    },
    [edges, setEdges, updateWorkflowEdges]
  );

  // 处理节点变化
  const handleNodesChange = useCallback(
    (changes: any) => {
      onNodesChange(changes);
      // 延迟更新store以避免性能问题
      setTimeout(() => {
        updateWorkflowNodes(nodes);
      }, 100);
    },
    [onNodesChange, nodes, updateWorkflowNodes]
  );

  // 处理边变化
  const handleEdgesChange = useCallback(
    (changes: any) => {
      onEdgesChange(changes);
      setTimeout(() => {
        updateWorkflowEdges(edges);
      }, 100);
    },
    [onEdgesChange, edges, updateWorkflowEdges]
  );

  // 处理拖拽放置
  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect();
      if (!reactFlowBounds || !reactFlowInstance.current) return;

      const type = event.dataTransfer.getData('application/json');
      if (!type) return;

      const { type: nodeType, data } = JSON.parse(type);
      const position = reactFlowInstance.current.project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });

      let newNodeData: any = {
        id: `${nodeType}-${Date.now()}`,
        type: nodeType,
      };

      // 根据节点类型设置特定数据
      switch (nodeType) {
        case 'textNode':
          newNodeData = {
            ...newNodeData,
            content: data.content || '新文字提示词',
          };
          break;
        case 'imageNode':
          newNodeData = {
            ...newNodeData,
            imageId: data.id,
            thumbnail_url: data.thumbnail_url,
            name: data.name,
          };
          break;
        case 'paramNode':
          newNodeData = {
            ...newNodeData,
            paramName: data.name,
            paramValue: data.defaultValue,
            paramType: data.type,
            options: data.options,
            min: data.min,
            max: data.max,
          };
          break;
      }

      const newNode: Node<NodeData> = {
        id: `${nodeType}-${Date.now()}`,
        type: nodeType,
        position,
        data: newNodeData,
      };

      const newNodes = [...nodes, newNode];
      setNodes(newNodes);
      updateWorkflowNodes(newNodes);
    },
    [nodes, setNodes, updateWorkflowNodes]
  );

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  // 处理节点选择
  const onNodeClick = useCallback(
    (event: React.MouseEvent, node: Node) => {
      setSelectedNodeId(node.id);
    },
    [setSelectedNodeId]
  );

  // 处理画布点击（取消选择）
  const onPaneClick = useCallback(() => {
    setSelectedNodeId(null);
  }, [setSelectedNodeId]);

  // 处理视口变化
  const onMove = useCallback(
    (event: MouseEvent | TouchEvent, viewport: { x: number; y: number; zoom: number }) => {
      updateWorkflowViewport(viewport);
    },
    [updateWorkflowViewport]
  );

  // 处理键盘事件
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Delete' || event.key === 'Backspace') {
        const selectedNodes = nodes.filter(node => node.selected);
        const selectedEdges = edges.filter(edge => edge.selected);

        if (selectedNodes.length > 0 || selectedEdges.length > 0) {
          const newNodes = nodes.filter(node => !node.selected);
          const newEdges = edges.filter(edge => !edge.selected);

          setNodes(newNodes);
          setEdges(newEdges);
          updateWorkflowNodes(newNodes);
          updateWorkflowEdges(newEdges);
          setSelectedNodeId(null);
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [nodes, edges, setNodes, setEdges, updateWorkflowNodes, updateWorkflowEdges, setSelectedNodeId]);

  return (
    <Box 
      ref={reactFlowWrapper}
      sx={{ 
        width: '100%', 
        height: '100vh',
        backgroundColor: '#f5f5f5'
      }}
    >
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={handleNodesChange}
        onEdgesChange={handleEdgesChange}
        onConnect={onConnect}
        onDrop={onDrop}
        onDragOver={onDragOver}
        onNodeClick={onNodeClick}
        onPaneClick={onPaneClick}
        onMove={onMove}
        nodeTypes={memoizedNodeTypes}
        onInit={(instance) => {
          reactFlowInstance.current = instance;
        }}
        defaultViewport={currentWorkflow.viewport}
        fitView
        attributionPosition="bottom-left"
      >
        <Background color="#aaa" gap={16} />
        <Controls />
        <MiniMap 
          style={{
            height: 120,
            backgroundColor: '#fff',
          }}
          zoomable
          pannable
        />
      </ReactFlow>
    </Box>
  );
};

// 包装组件以提供ReactFlow上下文
const CanvasWrapper: React.FC = () => {
  return (
    <ReactFlowProvider>
      <Canvas />
    </ReactFlowProvider>
  );
};

export default CanvasWrapper;
