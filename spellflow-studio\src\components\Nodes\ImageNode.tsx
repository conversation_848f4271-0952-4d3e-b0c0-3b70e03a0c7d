import React, { useState } from 'react';
import { Handle, Position, NodeProps } from 'reactflow';
import {
  Paper,
  Typography,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Chip,
  Grid,
} from '@mui/material';
import { Info as InfoIcon, Image as ImageIcon } from '@mui/icons-material';
import { ImageNodeData } from '../../types';
import { useAppStore } from '../../store';

const ImageNode: React.FC<NodeProps<ImageNodeData>> = ({ data, selected }) => {
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);
  const { imagePrompts } = useAppStore();

  // 根据imageId获取完整的图片信息
  const imageInfo = imagePrompts.find(img => img.id === data.imageId);

  const handleInfoClick = () => {
    setDetailDialogOpen(true);
  };

  const handleCloseDetail = () => {
    setDetailDialogOpen(false);
  };

  return (
    <>
      <Paper
        elevation={selected ? 8 : 2}
        sx={{
          padding: 2,
          minWidth: 180,
          maxWidth: 250,
          border: selected ? '2px solid #1976d2' : '1px solid #ddd',
          borderRadius: 2,
          backgroundColor: '#fff',
          cursor: 'pointer',
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            elevation: 4,
            transform: 'translateY(-1px)',
          },
        }}
      >
        {/* 输出端口 */}
        <Handle
          type="source"
          position={Position.Right}
          style={{
            background: '#1976d2',
            width: 12,
            height: 12,
            border: '2px solid #fff',
          }}
        />

        {/* 节点头部 */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <ImageIcon sx={{ color: '#1976d2', mr: 1, fontSize: 20 }} />
          <Typography variant="subtitle2" sx={{ flexGrow: 1, fontWeight: 'bold' }}>
            图片提示词
          </Typography>
          <IconButton 
            size="small" 
            onClick={handleInfoClick}
            sx={{ padding: 0.5 }}
          >
            <InfoIcon sx={{ fontSize: 16 }} />
          </IconButton>
        </Box>

        {/* 图片缩略图 */}
        <Box sx={{ mb: 1 }}>
          <img
            src={data.thumbnail_url}
            alt={data.name}
            style={{
              width: '100%',
              height: '100px',
              objectFit: 'cover',
              borderRadius: '4px',
              border: '1px solid #ddd',
            }}
          />
        </Box>

        {/* 图片名称 */}
        <Typography 
          variant="body2" 
          sx={{ 
            color: '#666',
            textAlign: 'center',
            fontWeight: 'medium',
          }}
        >
          {data.name}
        </Typography>
      </Paper>

      {/* 详情对话框 */}
      <Dialog 
        open={detailDialogOpen} 
        onClose={handleCloseDetail}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>图片提示词详情</DialogTitle>
        <DialogContent>
          {imageInfo && (
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <img
                  src={imageInfo.thumbnail_url}
                  alt={imageInfo.name}
                  style={{
                    width: '100%',
                    maxHeight: '300px',
                    objectFit: 'contain',
                    borderRadius: '8px',
                    border: '1px solid #ddd',
                  }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  {imageInfo.name}
                </Typography>
                
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    项目
                  </Typography>
                  <Chip label={imageInfo.metadata.project} size="small" />
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    风格
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {imageInfo.metadata.style}
                  </Typography>
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    材料
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {imageInfo.metadata.material}
                  </Typography>
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    环境
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {imageInfo.metadata.environment}
                  </Typography>
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    光影
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {imageInfo.metadata.lighting}
                  </Typography>
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    构图
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {imageInfo.metadata.composition}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDetail}>关闭</Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ImageNode;
