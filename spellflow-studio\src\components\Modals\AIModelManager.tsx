import React, { useState } from 'react';
import {
  <PERSON>alog,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Box,
  Typography,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  InputAdornment,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
} from '@mui/icons-material';
import { useAppStore } from '../../store';
import type { AIModelConfig } from '../../types';

interface AIModelManagerProps {
  open: boolean;
  onClose: () => void;
}

const AIModelManager: React.FC<AIModelManagerProps> = ({ open, onClose }) => {
  const { aiModels, addAIModel, updateAIModel, deleteAIModel } = useAppStore();
  
  const [editingModel, setEditingModel] = useState<AIModelConfig | null>(null);
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [showApiKey, setShowApiKey] = useState<{ [key: string]: boolean }>({});
  
  const [formData, setFormData] = useState<Partial<AIModelConfig>>({
    name: '',
    type: 'Stable Diffusion',
    api_endpoint: '',
    api_key: '',
  });

  const modelTypes = ['Stable Diffusion', 'Midjourney', 'DALL-E 3'];

  const handleAddNew = () => {
    setIsAddingNew(true);
    setFormData({
      name: '',
      type: 'Stable Diffusion',
      api_endpoint: '',
      api_key: '',
    });
  };

  const handleEdit = (model: AIModelConfig) => {
    setEditingModel(model);
    setFormData(model);
  };

  const handleSave = () => {
    if (!formData.name || !formData.api_endpoint) {
      alert('请填写必要信息');
      return;
    }

    const modelData: AIModelConfig = {
      id: editingModel?.id || `model-${Date.now()}`,
      name: formData.name!,
      type: formData.type as AIModelConfig['type'],
      api_endpoint: formData.api_endpoint!,
      api_key: formData.api_key || '',
    };

    if (editingModel) {
      updateAIModel(editingModel.id, modelData);
    } else {
      addAIModel(modelData);
    }

    handleCancel();
  };

  const handleCancel = () => {
    setEditingModel(null);
    setIsAddingNew(false);
    setFormData({
      name: '',
      type: 'Stable Diffusion',
      api_endpoint: '',
      api_key: '',
    });
  };

  const handleDelete = (id: string) => {
    if (confirm('确定要删除这个AI模型配置吗？')) {
      deleteAIModel(id);
    }
  };

  const toggleApiKeyVisibility = (modelId: string) => {
    setShowApiKey(prev => ({
      ...prev,
      [modelId]: !prev[modelId]
    }));
  };

  const maskApiKey = (apiKey: string) => {
    if (!apiKey) return '';
    return apiKey.length > 8 ? 
      `${apiKey.substring(0, 4)}${'*'.repeat(apiKey.length - 8)}${apiKey.substring(apiKey.length - 4)}` : 
      '*'.repeat(apiKey.length);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>AI模型管理</DialogTitle>
      <DialogContent>
        <Box sx={{ mb: 2 }}>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddNew}
            disabled={isAddingNew || editingModel !== null}
          >
            添加新模型
          </Button>
        </Box>

        {(isAddingNew || editingModel) && (
          <Paper sx={{ p: 2, mb: 2 }}>
            <Typography variant="h6" gutterBottom>
              {editingModel ? '编辑模型' : '添加新模型'}
            </Typography>
            
            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2, mb: 2 }}>
              <TextField
                label="模型名称"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                required
              />
              
              <FormControl required>
                <InputLabel>模型类型</InputLabel>
                <Select
                  value={formData.type}
                  onChange={(e) => setFormData({ ...formData, type: e.target.value as AIModelConfig['type'] })}
                  label="模型类型"
                >
                  {modelTypes.map((type) => (
                    <MenuItem key={type} value={type}>
                      {type}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>

            <TextField
              fullWidth
              label="API调用地址"
              value={formData.api_endpoint}
              onChange={(e) => setFormData({ ...formData, api_endpoint: e.target.value })}
              placeholder="http://127.0.0.1:7860/sdapi/v1/txt2img"
              sx={{ mb: 2 }}
              required
            />

            <TextField
              fullWidth
              label="API密钥"
              type="password"
              value={formData.api_key}
              onChange={(e) => setFormData({ ...formData, api_key: e.target.value })}
              placeholder="可选，如果API需要认证"
              sx={{ mb: 2 }}
            />

            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="contained"
                startIcon={<SaveIcon />}
                onClick={handleSave}
              >
                保存
              </Button>
              <Button
                variant="outlined"
                startIcon={<CancelIcon />}
                onClick={handleCancel}
              >
                取消
              </Button>
            </Box>
          </Paper>
        )}

        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>模型名称</TableCell>
                <TableCell>类型</TableCell>
                <TableCell>API地址</TableCell>
                <TableCell>API密钥</TableCell>
                <TableCell align="center">操作</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {aiModels.map((model) => (
                <TableRow key={model.id}>
                  <TableCell>{model.name}</TableCell>
                  <TableCell>{model.type}</TableCell>
                  <TableCell>
                    <Typography variant="body2" sx={{ 
                      maxWidth: 200, 
                      overflow: 'hidden', 
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}>
                      {model.api_endpoint}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="body2">
                        {showApiKey[model.id] ? model.api_key : maskApiKey(model.api_key)}
                      </Typography>
                      {model.api_key && (
                        <IconButton
                          size="small"
                          onClick={() => toggleApiKeyVisibility(model.id)}
                        >
                          {showApiKey[model.id] ? <VisibilityOffIcon /> : <VisibilityIcon />}
                        </IconButton>
                      )}
                    </Box>
                  </TableCell>
                  <TableCell align="center">
                    <IconButton
                      size="small"
                      onClick={() => handleEdit(model)}
                      disabled={isAddingNew || editingModel !== null}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleDelete(model.id)}
                      disabled={isAddingNew || editingModel !== null}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
              {aiModels.length === 0 && (
                <TableRow>
                  <TableCell colSpan={5} align="center">
                    <Typography color="text.secondary">
                      暂无AI模型配置，点击"添加新模型"开始配置
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>关闭</Button>
      </DialogActions>
    </Dialog>
  );
};

export default AIModelManager;
