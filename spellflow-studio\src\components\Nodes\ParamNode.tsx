import React, { useState } from 'react';
import { Handle, Position, NodeProps } from 'reactflow';
import {
  Paper,
  Typography,
  Box,
  Slider,
  Select,
  MenuItem,
  Switch,
  TextField,
  FormControl,
  InputLabel,
} from '@mui/material';
import { Settings as SettingsIcon } from '@mui/icons-material';
import type { ParamNodeData } from '../../types';

const ParamNode: React.FC<NodeProps<ParamNodeData>> = ({ data, selected }) => {
  const [paramValue, setParamValue] = useState(data.paramValue);

  const handleValueChange = (newValue: any) => {
    setParamValue(newValue);
    // TODO: 更新节点数据到store
  };

  const renderControl = () => {
    switch (data.paramType) {
      case 'slider':
        return (
          <Box sx={{ px: 1, mt: 1 }}>
            <Slider
              value={paramValue || data.min || 0}
              min={data.min || 0}
              max={data.max || 100}
              step={0.1}
              onChange={(_, value) => handleValueChange(value)}
              valueLabelDisplay="auto"
              size="small"
            />
            <Typography variant="caption" color="text.secondary" align="center" display="block">
              {paramValue || data.min || 0}
            </Typography>
          </Box>
        );

      case 'dropdown':
        return (
          <FormControl fullWidth size="small" sx={{ mt: 1 }}>
            <Select
              value={paramValue || (data.options && data.options[0]) || ''}
              onChange={(e) => handleValueChange(e.target.value)}
              displayEmpty
            >
              {data.options?.map((option, index) => (
                <MenuItem key={index} value={option}>
                  {option}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        );

      case 'toggle':
        return (
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 1 }}>
            <Switch
              checked={paramValue || false}
              onChange={(e) => handleValueChange(e.target.checked)}
              size="small"
            />
            <Typography variant="caption" sx={{ ml: 1 }}>
              {paramValue ? '开启' : '关闭'}
            </Typography>
          </Box>
        );

      case 'text':
        return (
          <TextField
            fullWidth
            size="small"
            value={paramValue || ''}
            onChange={(e) => handleValueChange(e.target.value)}
            placeholder="输入值..."
            sx={{ mt: 1 }}
          />
        );

      default:
        return (
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            未知参数类型
          </Typography>
        );
    }
  };

  return (
    <Paper
      elevation={selected ? 8 : 2}
      sx={{
        padding: 2,
        minWidth: 200,
        maxWidth: 280,
        border: selected ? '2px solid #1976d2' : '1px solid #ddd',
        borderRadius: 2,
        backgroundColor: '#fff',
        cursor: 'pointer',
        transition: 'all 0.2s ease-in-out',
        '&:hover': {
          elevation: 4,
          transform: 'translateY(-1px)',
        },
      }}
    >
      {/* 输出端口 */}
      <Handle
        type="source"
        position={Position.Right}
        style={{
          background: '#1976d2',
          width: 12,
          height: 12,
          border: '2px solid #fff',
        }}
      />

      {/* 节点头部 */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
        <SettingsIcon sx={{ color: '#1976d2', mr: 1, fontSize: 20 }} />
        <Typography variant="subtitle2" sx={{ flexGrow: 1, fontWeight: 'bold' }}>
          {data.paramName}
        </Typography>
      </Box>

      {/* 参数控件 */}
      {renderControl()}

      {/* 参数类型标识 */}
      <Typography 
        variant="caption" 
        sx={{ 
          color: '#999',
          display: 'block',
          textAlign: 'center',
          mt: 1,
          textTransform: 'uppercase',
          fontSize: '0.65rem',
        }}
      >
        {data.paramType}
      </Typography>
    </Paper>
  );
};

export default ParamNode;
