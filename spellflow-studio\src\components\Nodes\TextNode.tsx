import React, { useState } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import {
  Paper,
  Typography,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
} from '@mui/material';
import { Edit as EditIcon, TextFields as TextFieldsIcon } from '@mui/icons-material';
import type { TextNodeData } from '../../types';

const TextNode: React.FC<NodeProps<TextNodeData>> = ({ data, selected }) => {
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editContent, setEditContent] = useState(data.content || '');

  const handleEditClick = () => {
    setEditContent(data.content || '');
    setEditDialogOpen(true);
  };

  const handleSaveEdit = () => {
    // TODO: 更新节点数据
    // 这里需要通过store或回调函数更新节点内容
    setEditDialogOpen(false);
  };

  const handleCancelEdit = () => {
    setEditContent(data.content || '');
    setEditDialogOpen(false);
  };

  const displayText = data.content || '空文本';
  const truncatedText = displayText.length > 30 ? 
    `${displayText.substring(0, 30)}...` : 
    displayText;

  return (
    <>
      <Paper
        elevation={selected ? 8 : 2}
        sx={{
          padding: 2,
          minWidth: 200,
          maxWidth: 300,
          border: selected ? '2px solid #1976d2' : '1px solid #e0e0e0',
          borderRadius: 3,
          backgroundColor: '#fff',
          cursor: 'pointer',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          position: 'relative',
          overflow: 'hidden',
          '&:hover': {
            boxShadow: '0 8px 25px rgba(25, 118, 210, 0.15)',
            transform: 'translateY(-2px)',
            borderColor: '#1976d2',
          },
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: 3,
            background: 'linear-gradient(90deg, #1976d2, #42a5f5)',
            opacity: selected ? 1 : 0,
            transition: 'opacity 0.3s ease',
          },
        }}
      >
        {/* 输出端口 */}
        <Handle
          type="source"
          position={Position.Right}
          style={{
            background: '#1976d2',
            width: 12,
            height: 12,
            border: '2px solid #fff',
          }}
        />

        {/* 节点头部 */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <TextFieldsIcon sx={{ color: '#1976d2', mr: 1, fontSize: 20 }} />
          <Typography variant="subtitle2" sx={{ flexGrow: 1, fontWeight: 'bold' }}>
            文字提示词
          </Typography>
          <IconButton 
            size="small" 
            onClick={handleEditClick}
            sx={{ padding: 0.5 }}
          >
            <EditIcon sx={{ fontSize: 16 }} />
          </IconButton>
        </Box>

        {/* 文本内容 */}
        <Typography 
          variant="body2" 
          sx={{ 
            color: '#666',
            lineHeight: 1.4,
            wordBreak: 'break-word',
            minHeight: '1.4em',
          }}
        >
          {truncatedText}
        </Typography>
      </Paper>

      {/* 编辑对话框 */}
      <Dialog 
        open={editDialogOpen} 
        onClose={handleCancelEdit}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>编辑文字提示词</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="提示词内容"
            fullWidth
            multiline
            rows={4}
            variant="outlined"
            value={editContent}
            onChange={(e) => setEditContent(e.target.value)}
            placeholder="请输入提示词内容..."
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelEdit}>取消</Button>
          <Button onClick={handleSaveEdit} variant="contained">
            保存
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default TextNode;
