import React from 'react';
import {
  Drawer,
  Box,
  Tabs,
  Tab,
  Typography,
  Collapse,
  List,
  ListItem,
  ListItemText,
  ListItemButton,
  IconButton,
  Chip,
} from '@mui/material';
import {
  TextFields as TextFieldsIcon,
  Image as ImageIcon,
  Settings as SettingsIcon,
  ExpandLess,
  ExpandMore,
  Edit as EditIcon,
} from '@mui/icons-material';
import { useAppStore } from '../../store';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`sidebar-tabpanel-${index}`}
      aria-labelledby={`sidebar-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 2 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const Sidebar: React.FC = () => {
  const { 
    sidebarCollapsed, 
    activeTab, 
    setActiveTab,
    textPrompts,
    imagePrompts,
    categories,
    parameters 
  } = useAppStore();

  const [expandedCategories, setExpandedCategories] = React.useState<string[]>([]);
  const [tabValue, setTabValue] = React.useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    const tabs: ('text' | 'image' | 'param')[] = ['text', 'image', 'param'];
    setActiveTab(tabs[newValue]);
  };

  const handleCategoryToggle = (categoryId: string) => {
    setExpandedCategories(prev => 
      prev.includes(categoryId) 
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  const handleDragStart = (event: React.DragEvent, item: any, type: string) => {
    event.dataTransfer.setData('application/json', JSON.stringify({
      type,
      data: item
    }));
  };

  const drawerWidth = 320;

  return (
    <Drawer
      variant="persistent"
      anchor="left"
      open={!sidebarCollapsed}
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
          marginTop: '64px', // AppBar height
          height: 'calc(100vh - 64px)',
        },
      }}
    >
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="模块库标签">
          <Tab 
            icon={<TextFieldsIcon />} 
            label="文字" 
            id="sidebar-tab-0"
            aria-controls="sidebar-tabpanel-0"
          />
          <Tab 
            icon={<ImageIcon />} 
            label="图片" 
            id="sidebar-tab-1"
            aria-controls="sidebar-tabpanel-1"
          />
          <Tab 
            icon={<SettingsIcon />} 
            label="参数" 
            id="sidebar-tab-2"
            aria-controls="sidebar-tabpanel-2"
          />
        </Tabs>
      </Box>

      {/* 文字提示词模块 */}
      <TabPanel value={tabValue} index={0}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">文字提示词</Typography>
          <IconButton size="small" title="管理">
            <EditIcon />
          </IconButton>
        </Box>
        
        <List dense>
          {categories.map((category) => {
            const categoryPrompts = textPrompts.filter(p => p.category_id === category.id);
            const isExpanded = expandedCategories.includes(category.id);
            
            return (
              <React.Fragment key={category.id}>
                <ListItemButton onClick={() => handleCategoryToggle(category.id)}>
                  <ListItemText 
                    primary={category.name} 
                    secondary={`${categoryPrompts.length} 项`}
                  />
                  {isExpanded ? <ExpandLess /> : <ExpandMore />}
                </ListItemButton>
                
                <Collapse in={isExpanded} timeout="auto" unmountOnExit>
                  <List component="div" disablePadding>
                    {categoryPrompts.map((prompt) => (
                      <ListItem
                        key={prompt.id}
                        sx={{ pl: 4, cursor: 'grab' }}
                        draggable
                        onDragStart={(e) => handleDragStart(e, prompt, 'textNode')}
                      >
                        <ListItemText 
                          primary={
                            <Chip 
                              label={prompt.content.length > 20 ? 
                                `${prompt.content.substring(0, 20)}...` : 
                                prompt.content
                              }
                              size="small"
                              variant="outlined"
                            />
                          }
                        />
                      </ListItem>
                    ))}
                  </List>
                </Collapse>
              </React.Fragment>
            );
          })}
        </List>
      </TabPanel>

      {/* 图片提示词模块 */}
      <TabPanel value={tabValue} index={1}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">图片提示词</Typography>
          <IconButton size="small" title="管理">
            <EditIcon />
          </IconButton>
        </Box>
        
        <Box sx={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fill, minmax(120px, 1fr))', 
          gap: 2 
        }}>
          {imagePrompts.map((image) => (
            <Box
              key={image.id}
              sx={{
                cursor: 'grab',
                border: '1px solid #ddd',
                borderRadius: 1,
                overflow: 'hidden',
                '&:hover': {
                  boxShadow: 2,
                },
              }}
              draggable
              onDragStart={(e) => handleDragStart(e, image, 'imageNode')}
            >
              <img
                src={image.thumbnail_url}
                alt={image.name}
                style={{
                  width: '100%',
                  height: '80px',
                  objectFit: 'cover',
                }}
              />
              <Typography 
                variant="caption" 
                sx={{ 
                  p: 0.5, 
                  display: 'block',
                  fontSize: '0.7rem',
                  lineHeight: 1.2
                }}
              >
                {image.name}
              </Typography>
            </Box>
          ))}
        </Box>
      </TabPanel>

      {/* 参数模块 */}
      <TabPanel value={tabValue} index={2}>
        <Typography variant="h6" sx={{ mb: 2 }}>AI模型参数</Typography>
        
        <List dense>
          {parameters.map((param) => (
            <ListItem
              key={param.id}
              sx={{ cursor: 'grab' }}
              draggable
              onDragStart={(e) => handleDragStart(e, param, 'paramNode')}
            >
              <ListItemText 
                primary={param.name}
                secondary={`类型: ${param.type}`}
              />
            </ListItem>
          ))}
        </List>
      </TabPanel>
    </Drawer>
  );
};

export default Sidebar;
