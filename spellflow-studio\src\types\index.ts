// 基础数据类型定义

export interface TextPromptModule {
  id: string;
  content: string;
  category_id: string;
}

export interface ImagePromptModule {
  id: string;
  name: string;
  thumbnail_url: string;
  metadata: {
    project: string;
    style: string;
    material: string;
    environment: string;
    lighting: string;
    composition: string;
  };
}

export interface AIModelConfig {
  id: string;
  name: string;
  type: 'Stable Diffusion' | 'Midjourney' | 'DALL-E 3';
  api_endpoint: string;
  api_key: string;
}

export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  workflow_data: {
    nodes: any[];
    edges: any[];
    viewport: {
      x: number;
      y: number;
      zoom: number;
    };
  };
}

export interface Category {
  id: string;
  name: string;
  parent_id?: string;
}

// 节点类型定义
export type NodeType = 'textNode' | 'imageNode' | 'paramNode' | 'outputNode';

export interface BaseNodeData {
  id: string;
  type: NodeType;
}

export interface TextNodeData extends BaseNodeData {
  type: 'textNode';
  content: string;
}

export interface ImageNodeData extends BaseNodeData {
  type: 'imageNode';
  imageId: string;
  thumbnail_url: string;
  name: string;
}

export interface ParamNodeData extends BaseNodeData {
  type: 'paramNode';
  paramName: string;
  paramValue: any;
  paramType: 'slider' | 'dropdown' | 'toggle' | 'text';
  options?: any[];
  min?: number;
  max?: number;
}

export interface OutputNodeData extends BaseNodeData {
  type: 'outputNode';
  finalPrompt: string;
}

export type NodeData = TextNodeData | ImageNodeData | ParamNodeData | OutputNodeData;

// 参数类型定义
export interface ParameterConfig {
  id: string;
  name: string;
  type: 'slider' | 'dropdown' | 'toggle' | 'text';
  defaultValue: any;
  options?: any[];
  min?: number;
  max?: number;
  step?: number;
}

// React Flow 节点属性类型
export interface NodeProps<T = any> {
  id: string;
  data: T;
  selected?: boolean;
  isConnectable?: boolean;
  zIndex?: number;
  xPos: number;
  yPos: number;
  dragging: boolean;
}
