这份文档将详细描述“咒语流工作室 (SpellFlow Studio)”前端需要实现的所有功能、界面、交互和技术要求，以便前端开发团队可以据此进行设计和开发。后端部分将仅通过定义前端需要的数据结构和API契约来体现。

-----

### **产品开发文档 (PRD): 咒语流工作室 (前端部分)**

| 文档名称 | 咒语流工作室 - 前端产品开发文档 |
| :--- | :--- |
| **版本** | v1.0 |
| **发布日期** | 2025年6月27日 |
| **撰写人** | Gemini AI |
| **目标读者**| 前端开发团队、UI/UX设计师、产品经理、测试工程师 |

#### 1\. 产品概述 (Product Overview)

本文档定义了“咒语流工作室”的前端应用。这是一个基于Web的可视化AI提示词（咒语）构建工具。用户通过在画布上拖拽、连接不同类型的“模块节点”（文字、图片、参数），以图形化的方式构建出一个结构化、逻辑清晰的“咒语集”。该前端应用负责所有用户交互、界面展示、工作流编辑和状态管理，并通过API与后端服务进行数据交换和任务执行。

#### 2\. 目标用户

  * **AI艺术家/设计师:** 追求创作流程的精确控制和可复现性。
  * **内容创作者/营销团队:** 追求内容生产的效率和品牌风格的一致性。
  * **AI应用开发者/爱好者:** 追求一个统一、灵活的平台来实验和调试不同的模型与Prompt组合。

#### 3\. 设计原则与技术栈建议

  * **设计原则:**

      * **直观易用:** 核心拖拽、连接操作符合用户直觉，降低学习成本。
      * **模块化与可扩展:** UI组件和功能逻辑应高度模块化，便于未来新增节点类型和功能。
      * **高响应性与即时反馈:** 所有用户操作都应有即时的视觉反馈，如节点连接、咒语预览等。
      * **状态持久化:** 用户的工作流（即画布上的内容）在刷新页面后应能自动恢复，避免数据丢失。

  * **技术栈建议:**

      * **框架:** `React (v18+)` 或 `Vue (v3+)`
      * **语言:** `TypeScript` (强制，以保证代码健壮性和类型安全)
      * **节点式UI库:** `React Flow` (强烈推荐) 或类似的库，用于处理画布、节点、连接的所有交互。
      * **状态管理:** `Zustand` 或 `Redux Toolkit`，用于管理全局状态，如模块库数据、AI模型列表、当前工作流状态。
      * **UI组件库:** `MUI`, `Ant Design` 或 `Radix UI` (结合 `Tailwind CSS`)，用于快速构建表单、弹窗、菜单等非核心画布UI。
      * **数据请求:** `Axios` 或 `TanStack Query`

#### 4\. 核心功能模块详细需求 (Frontend)

##### 4.1. 主界面布局 (Main Interface Layout)

应用应采用三栏式布局：

1.  **左侧边栏 (模块库):** 固定宽度，可折叠。用于展示所有可用的提示词模块。
2.  **中央区域 (工作流画布):** 占据主要空间，是用户搭建工作流的核心区域。
3.  **顶部工具栏:** 包含全局操作，如“保存为模板”、“加载模板”、“执行生成”等。

##### 4.2. 可视化工作流编辑器 (The Canvas)

  * **画布基本操作:**
      * 支持鼠标滚轮 **缩放 (Zoom In/Out)**。
      * 支持按住鼠标中键或`Space`键+左键 **拖拽平移 (Pan)** 画布。
  * **节点操作:**
      * **添加:** 从左侧模块库拖拽模块到画布上，生成一个对应的节点。
      * **移动:** 在画布上自由拖动节点。
      * **选中:** 单击选中节点，被选中的节点应有明显高亮状态。
      * **删除:** 选中节点后按 `Delete` / `Backspace` 键，或通过右键菜单删除。
      * **连接:** 从一个节点的输出端口 (handle) 拖拽出一条线，连接到另一个节点的输入端口。连接线应平滑绘制。
  * **连接线 (Edge) 操作:**
      * **创建:** 见上文。
      * **删除:** 选中连接线（单击），按 `Delete` 键删除。

##### 4.3. 模块库 (The Library Sidebar)

模块库应使用标签页 (Tabs) 切换不同类型的模块。

  * **a. 文字提示词模块 (Text Prompts)**

      * **UI:** 使用可折叠的树状结构 (Tree View) 展示用户自定义的分类（如“风格”、“人物”、“场景”）。
      * **交互:**
          * 点击分类可展开/折叠。
          * 每个分类下是具体的文字提示词项。
          * 将提示词项拖拽到画布上，即可创建一个“文字节点”。
      * **管理功能:** 应有“管理”按钮，点击后弹出 **模态框 (Modal)**，在模态框内实现对文字提示词的增、删、改、查及分类管理。

  * **b. 图片提示词模块 (Image Prompts)**

      * **UI:** 以缩略图网格的形式展示用户上传的图片。
      * **交互:**
          * 鼠标悬浮在图片上时，显示图片名称或核心标签。
          * **单击图片**：不拖拽，而是单击图片，会弹出一个 **详情侧边栏或模态框**，展示并允许编辑这张图片绑定的结构化提示词集（项目、风格、材料、环境、光影、构图）。
          * **拖拽图片** 到画布上，即可创建一个“图片节点”。

  * **c. AI模型参数模块 (Parameter Prompts)**

      * **UI:** 以列表形式展示各类参数。
      * **交互:** 根据参数类型提供不同的UI控件。
          * **数值范围型 (e.g., CFG Scale):** 滑块 (Slider)。
          * **固定选项型 (e.g., Midjourney Version):** 下拉菜单 (Dropdown)。
          * **开关型 (e.g., --style raw):** 开关 (Switch/Toggle)。
          * **文本型 (e.g., --seed):** 文本输入框。
          * 将参数项拖拽到画布上，即可创建一个“参数节点”。

##### 4.4. 节点详细设计 (Node on Canvas)

每个节点都应包含一个标题和若干输入/输出端口。

  * **文字节点 (Text Node):**
      * **UI:** 显示文字提示词的片段。节点上应有一个小图标或按钮，点击可弹出文本框完整编辑该节点的文字。
  * **图片节点 (Image Node):**
      * **UI:** 在节点内部显示该图片的缩略图。
  * **参数节点 (Parameter Node):**
      * **UI:** 显示参数名称（如 `Aspect Ratio`），并在节点内部直接提供其对应的UI控件（如显示`16:9`的下拉菜单），允许在节点上直接修改参数值。
  * **最终咒语集节点 (Output/Result Node):**
      * **UI:** 这是一个特殊的、通常默认存在于画布上的节点。它有一个只读的多行文本框。
      * **功能:** 该文本框 **实时、动态地** 显示所有连接到它的节点所组合成的最终Prompt字符串。这是核心的即时反馈功能。

##### 4.5. AI 模型管理器 (UI)

  * **入口:** 顶部工具栏应有一个“设置”或“AI模型”按钮。
  * **UI:** 点击后弹出 **模态框**。
      * **列表视图:** 显示一个表格，列出所有已添加的AI模型（名称、类型）。每行都有“编辑”和“删除”按钮。
      * **添加/编辑表单:** 点击“添加新模型”或“编辑”，会弹出另一个表单模态框或切换视图。表单包含以下输入字段：
          * `模型名称` (文本输入)
          * `模型类型` (下拉菜单: Midjourney, Stable Diffusion, DALL-E等)
          * `API调用地址` (文本输入)
          * `API-Key` (密码类型输入，带显示/隐藏切换按钮)
          * “保存”和“取消”按钮。

##### 4.6. 工作流模板系统 (UI)

  * **保存模板:**
      * 顶部工具栏有一个“另存为模板”按钮。
      * 点击后弹出对话框，要求输入`模板名称`和`模板描述`。
  * **加载模板:**
      * 顶部工具栏有一个“加载工作流”或“模板”按钮。
      * 点击后弹出一个模态框，列表显示所有已保存的模板。
      * 每个模板项旁有“加载”和“删除”按钮。点击“加载”，会清空当前画布并载入模板内容。加载前应有二次确认提示，防止误操作。

#### 5\. 前后端交互定义 (Frontend API Contract)

前端需要以下数据结构。后端API应提供相应的`GET`, `POST`, `PUT`, `DELETE`接口。

  * **文字提示词模块 (`TextPromptModule`)**

    ```json
    {
      "id": "uuid-text-001",
      "content": "a beautiful girl",
      "category_id": "uuid-category-001"
    }
    ```

  * **图片提示词模块 (`ImagePromptModule`)**

    ```json
    {
      "id": "uuid-image-001",
      "name": "Cyberpunk Street View",
      "thumbnail_url": "https://.../thumb.jpg",
      "metadata": {
        "project": "Sci-Fi World",
        "style": "cinematic, photorealistic, cyberpunk",
        "material": "wet asphalt, chrome metal, neon lights",
        "environment": "rainy night, futuristic city, skyscrapers",
        "lighting": "dramatic lighting, volumetric haze, reflective surfaces",
        "composition": "wide-angle shot, leading lines"
      }
    }
    ```

  * **AI模型配置 (`AIModelConfig`)**

    ```json
    {
      "id": "uuid-model-001",
      "name": "My Stable Diffusion XL",
      "type": "Stable Diffusion", // Enum: "Stable Diffusion", "Midjourney", "DALL-E 3"
      "api_endpoint": "http://127.0.0.1:7860/sdapi/v1/txt2img",
      "api_key": "user-encrypted-key-string" // 后端处理加密，前端只需提交
    }
    ```

  * **工作流模板 (`WorkflowTemplate`)**

    ```json
    {
      "id": "uuid-template-001",
      "name": "High-Quality Product Shot",
      "description": "Template for generating photorealistic product images on a clean background.",
      "workflow_data": { // 这个数据结构由 React Flow 这类库直接导出
        "nodes": [
          { "id": "node-1", "type": "textNode", "position": { "x": 100, "y": 50 }, "data": { "content": "a bottle of perfume" } },
          { "id": "node-2", "type": "paramNode", "position": { "x": 100, "y": 150 }, "data": { "param": "--ar 1:1" } }
        ],
        "edges": [
          { "id": "edge-1-2", "source": "node-1", "target": "node-2" }
        ],
        "viewport": { "x": 0, "y": 0, "zoom": 1 }
      }
    }
    ```

#### 6\. 非功能性需求

  * **性能:** 页面首次加载时间应小于3秒。画布上有超过50个节点时，拖拽、缩放等操作仍应保持流畅（\>30 FPS）。
  * **兼容性:** 完美兼容最新版本的 Chrome, Firefox, Safari, Edge 浏览器。
  * **响应式设计:** 优先保证在桌面和平板设备上的良好体验。在手机端，可以考虑提供一个只读的、简化的视图，或明确提示用户在桌面端使用以获得完整体验。
  * **数据安全:** 所有API Key和敏感信息在提交时必须使用HTTPS。前端代码中不应硬编码任何密钥。

#### 7\. 附录：名词解释

  * **节点 (Node):** 画布上的一个可操作的功能块，代表一种类型的提示词（文字、图片、参数）。
  * **边 (Edge):** 连接两个节点的线，定义了数据流和组合逻辑。
  * **工作流 (Workflow):** 画布上所有节点和边的集合，代表一个完整的咒语构建方案。
  * **咒语集 (Spellbook/Prompt Set):** 最终由工作流汇聚生成、可发送给AI模型的完整字符串。