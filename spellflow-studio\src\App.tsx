import React, { useEffect } from 'react';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import MainLayout from './components/Layout/MainLayout';
import { useAppStore } from './store';
import theme from './theme';

function App() {
  const {
    setCategories,
    setTextPrompts,
    setImagePrompts,
    setParameters,
    updateWorkflowNodes
  } = useAppStore();

  // 初始化示例数据
  useEffect(() => {
    // 初始化分类
    setCategories([
      { id: 'cat-1', name: '风格' },
      { id: 'cat-2', name: '人物' },
      { id: 'cat-3', name: '场景' },
    ]);

    // 初始化文字提示词
    setTextPrompts([
      { id: 'text-1', content: 'a beautiful girl', category_id: 'cat-2' },
      { id: 'text-2', content: 'photorealistic', category_id: 'cat-1' },
      { id: 'text-3', content: 'cyberpunk city', category_id: 'cat-3' },
      { id: 'text-4', content: 'dramatic lighting', category_id: 'cat-1' },
    ]);

    // 初始化图片提示词
    setImagePrompts([
      {
        id: 'img-1',
        name: 'Cyberpunk Street',
        thumbnail_url: 'https://via.placeholder.com/150x100/4a90e2/ffffff?text=Cyberpunk',
        metadata: {
          project: 'Sci-Fi World',
          style: 'cinematic, photorealistic, cyberpunk',
          material: 'wet asphalt, chrome metal, neon lights',
          environment: 'rainy night, futuristic city, skyscrapers',
          lighting: 'dramatic lighting, volumetric haze, reflective surfaces',
          composition: 'wide-angle shot, leading lines'
        }
      },
      {
        id: 'img-2',
        name: 'Fantasy Forest',
        thumbnail_url: 'https://via.placeholder.com/150x100/50c878/ffffff?text=Fantasy',
        metadata: {
          project: 'Fantasy World',
          style: 'magical, ethereal, fantasy art',
          material: 'ancient trees, moss, magical crystals',
          environment: 'enchanted forest, mystical atmosphere',
          lighting: 'soft magical glow, dappled sunlight',
          composition: 'depth of field, mystical mood'
        }
      }
    ]);

    // 初始化参数
    setParameters([
      {
        id: 'param-1',
        name: 'CFG Scale',
        type: 'slider',
        defaultValue: 7.5,
        min: 1,
        max: 20,
        step: 0.5
      },
      {
        id: 'param-2',
        name: 'Aspect Ratio',
        type: 'dropdown',
        defaultValue: '1:1',
        options: ['1:1', '16:9', '9:16', '4:3', '3:4']
      },
      {
        id: 'param-3',
        name: 'Style Raw',
        type: 'toggle',
        defaultValue: false
      },
      {
        id: 'param-4',
        name: 'Seed',
        type: 'text',
        defaultValue: ''
      }
    ]);

    // 初始化默认的输出节点
    updateWorkflowNodes([
      {
        id: 'output-1',
        type: 'outputNode',
        position: { x: 400, y: 200 },
        data: {
          id: 'output-1',
          type: 'outputNode',
          finalPrompt: ''
        }
      }
    ]);
  }, [setCategories, setTextPrompts, setImagePrompts, setParameters, updateWorkflowNodes]);

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <MainLayout />
    </ThemeProvider>
  );
}

export default App;
