import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Box,
  Typography,
  Card,
  CardContent,
  CardActions,
  Grid,
} from '@mui/material';
import {
  Save as SaveIcon,
  FolderOpen as FolderOpenIcon,
  Delete as DeleteIcon,
  Description as DescriptionIcon,
} from '@mui/icons-material';
import { useAppStore } from '../../store';
import { WorkflowTemplate } from '../../types';

interface TemplateManagerProps {
  open: boolean;
  onClose: () => void;
  mode: 'save' | 'load';
}

const TemplateManager: React.FC<TemplateManagerProps> = ({ open, onClose, mode }) => {
  const { 
    templates, 
    currentWorkflow, 
    addTemplate, 
    deleteTemplate,
    setCurrentWorkflow 
  } = useAppStore();

  const [templateName, setTemplateName] = useState('');
  const [templateDescription, setTemplateDescription] = useState('');

  const handleSaveTemplate = () => {
    if (!templateName.trim()) {
      alert('请输入模板名称');
      return;
    }

    const newTemplate: WorkflowTemplate = {
      id: `template-${Date.now()}`,
      name: templateName.trim(),
      description: templateDescription.trim(),
      workflow_data: {
        nodes: currentWorkflow.nodes,
        edges: currentWorkflow.edges,
        viewport: currentWorkflow.viewport,
      },
    };

    addTemplate(newTemplate);
    setTemplateName('');
    setTemplateDescription('');
    onClose();
  };

  const handleLoadTemplate = (template: WorkflowTemplate) => {
    if (currentWorkflow.nodes.length > 0 || currentWorkflow.edges.length > 0) {
      if (!confirm('加载模板将清空当前工作流，确定继续吗？')) {
        return;
      }
    }

    setCurrentWorkflow(template.workflow_data);
    onClose();
  };

  const handleDeleteTemplate = (id: string) => {
    if (confirm('确定要删除这个模板吗？')) {
      deleteTemplate(id);
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('zh-CN');
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {mode === 'save' ? '保存工作流模板' : '加载工作流模板'}
      </DialogTitle>
      <DialogContent>
        {mode === 'save' ? (
          <Box>
            <TextField
              fullWidth
              label="模板名称"
              value={templateName}
              onChange={(e) => setTemplateName(e.target.value)}
              margin="normal"
              required
            />
            <TextField
              fullWidth
              label="模板描述"
              value={templateDescription}
              onChange={(e) => setTemplateDescription(e.target.value)}
              margin="normal"
              multiline
              rows={3}
              placeholder="描述这个模板的用途和特点..."
            />
            <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
              <Typography variant="body2" color="text.secondary">
                当前工作流包含：
              </Typography>
              <Typography variant="body2">
                • {currentWorkflow.nodes.length} 个节点
              </Typography>
              <Typography variant="body2">
                • {currentWorkflow.edges.length} 个连接
              </Typography>
            </Box>
          </Box>
        ) : (
          <Box>
            {templates.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <DescriptionIcon sx={{ fontSize: 64, color: 'grey.400', mb: 2 }} />
                <Typography variant="h6" color="text.secondary">
                  暂无保存的模板
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  创建一些工作流并保存为模板后，就可以在这里加载了
                </Typography>
              </Box>
            ) : (
              <Grid container spacing={2}>
                {templates.map((template) => (
                  <Grid item xs={12} sm={6} key={template.id}>
                    <Card>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          {template.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" paragraph>
                          {template.description || '无描述'}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          节点: {template.workflow_data.nodes.length} | 
                          连接: {template.workflow_data.edges.length}
                        </Typography>
                        <br />
                        <Typography variant="caption" color="text.secondary">
                          创建时间: {formatDate(parseInt(template.id.split('-')[1]))}
                        </Typography>
                      </CardContent>
                      <CardActions>
                        <Button
                          size="small"
                          startIcon={<FolderOpenIcon />}
                          onClick={() => handleLoadTemplate(template)}
                        >
                          加载
                        </Button>
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteTemplate(template.id)}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </CardActions>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        {mode === 'save' ? (
          <>
            <Button onClick={onClose}>取消</Button>
            <Button
              variant="contained"
              startIcon={<SaveIcon />}
              onClick={handleSaveTemplate}
              disabled={!templateName.trim()}
            >
              保存模板
            </Button>
          </>
        ) : (
          <Button onClick={onClose}>关闭</Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default TemplateManager;
