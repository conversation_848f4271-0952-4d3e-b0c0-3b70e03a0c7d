import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  TextField,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Box,
  Typography,
  Divider,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import { useAppStore } from '../../store';
import { TextPromptModule, Category } from '../../types';

interface TextPromptManagerProps {
  open: boolean;
  onClose: () => void;
}

const TextPromptManager: React.FC<TextPromptManagerProps> = ({ open, onClose }) => {
  const { 
    textPrompts, 
    categories, 
    addTextPrompt, 
    updateTextPrompt, 
    deleteTextPrompt,
    addCategory,
    updateCategory,
    deleteCategory 
  } = useAppStore();

  const [editingPrompt, setEditingPrompt] = useState<TextPromptModule | null>(null);
  const [newPromptContent, setNewPromptContent] = useState('');
  const [newPromptCategory, setNewPromptCategory] = useState('');
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [newCategoryName, setNewCategoryName] = useState('');

  const handleAddPrompt = () => {
    if (newPromptContent.trim() && newPromptCategory) {
      const newPrompt: TextPromptModule = {
        id: `text-${Date.now()}`,
        content: newPromptContent.trim(),
        category_id: newPromptCategory,
      };
      addTextPrompt(newPrompt);
      setNewPromptContent('');
      setNewPromptCategory('');
    }
  };

  const handleEditPrompt = (prompt: TextPromptModule) => {
    setEditingPrompt(prompt);
  };

  const handleSavePrompt = () => {
    if (editingPrompt) {
      updateTextPrompt(editingPrompt.id, editingPrompt);
      setEditingPrompt(null);
    }
  };

  const handleCancelEdit = () => {
    setEditingPrompt(null);
  };

  const handleDeletePrompt = (id: string) => {
    deleteTextPrompt(id);
  };

  const handleAddCategory = () => {
    if (newCategoryName.trim()) {
      const newCategory: Category = {
        id: `cat-${Date.now()}`,
        name: newCategoryName.trim(),
      };
      addCategory(newCategory);
      setNewCategoryName('');
    }
  };

  const handleEditCategory = (category: Category) => {
    setEditingCategory(category);
  };

  const handleSaveCategory = () => {
    if (editingCategory) {
      updateCategory(editingCategory.id, editingCategory);
      setEditingCategory(null);
    }
  };

  const handleDeleteCategory = (id: string) => {
    // 检查是否有提示词使用此分类
    const hasPrompts = textPrompts.some(prompt => prompt.category_id === id);
    if (hasPrompts) {
      alert('无法删除：该分类下还有提示词');
      return;
    }
    deleteCategory(id);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>文字提示词管理</DialogTitle>
      <DialogContent>
        <Box sx={{ display: 'flex', gap: 3 }}>
          {/* 分类管理 */}
          <Box sx={{ flex: 1 }}>
            <Typography variant="h6" gutterBottom>
              分类管理
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
              <TextField
                size="small"
                placeholder="新分类名称"
                value={newCategoryName}
                onChange={(e) => setNewCategoryName(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleAddCategory()}
              />
              <Button
                variant="contained"
                size="small"
                startIcon={<AddIcon />}
                onClick={handleAddCategory}
              >
                添加
              </Button>
            </Box>

            <List dense>
              {categories.map((category) => (
                <ListItem key={category.id}>
                  {editingCategory?.id === category.id ? (
                    <Box sx={{ display: 'flex', gap: 1, width: '100%' }}>
                      <TextField
                        size="small"
                        value={editingCategory.name}
                        onChange={(e) => setEditingCategory({
                          ...editingCategory,
                          name: e.target.value
                        })}
                        sx={{ flex: 1 }}
                      />
                      <IconButton size="small" onClick={handleSaveCategory}>
                        <SaveIcon />
                      </IconButton>
                      <IconButton size="small" onClick={() => setEditingCategory(null)}>
                        <CancelIcon />
                      </IconButton>
                    </Box>
                  ) : (
                    <>
                      <ListItemText 
                        primary={category.name}
                        secondary={`${textPrompts.filter(p => p.category_id === category.id).length} 项`}
                      />
                      <ListItemSecondaryAction>
                        <IconButton size="small" onClick={() => handleEditCategory(category)}>
                          <EditIcon />
                        </IconButton>
                        <IconButton size="small" onClick={() => handleDeleteCategory(category.id)}>
                          <DeleteIcon />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </>
                  )}
                </ListItem>
              ))}
            </List>
          </Box>

          <Divider orientation="vertical" flexItem />

          {/* 提示词管理 */}
          <Box sx={{ flex: 2 }}>
            <Typography variant="h6" gutterBottom>
              提示词管理
            </Typography>

            <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
              <TextField
                size="small"
                placeholder="新提示词内容"
                value={newPromptContent}
                onChange={(e) => setNewPromptContent(e.target.value)}
                sx={{ flex: 1 }}
              />
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>分类</InputLabel>
                <Select
                  value={newPromptCategory}
                  onChange={(e) => setNewPromptCategory(e.target.value)}
                  label="分类"
                >
                  {categories.map((category) => (
                    <MenuItem key={category.id} value={category.id}>
                      {category.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <Button
                variant="contained"
                size="small"
                startIcon={<AddIcon />}
                onClick={handleAddPrompt}
                disabled={!newPromptContent.trim() || !newPromptCategory}
              >
                添加
              </Button>
            </Box>

            <List dense sx={{ maxHeight: 400, overflow: 'auto' }}>
              {textPrompts.map((prompt) => {
                const category = categories.find(c => c.id === prompt.category_id);
                return (
                  <ListItem key={prompt.id}>
                    {editingPrompt?.id === prompt.id ? (
                      <Box sx={{ width: '100%' }}>
                        <TextField
                          fullWidth
                          multiline
                          rows={2}
                          value={editingPrompt.content}
                          onChange={(e) => setEditingPrompt({
                            ...editingPrompt,
                            content: e.target.value
                          })}
                          sx={{ mb: 1 }}
                        />
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Button size="small" startIcon={<SaveIcon />} onClick={handleSavePrompt}>
                            保存
                          </Button>
                          <Button size="small" startIcon={<CancelIcon />} onClick={handleCancelEdit}>
                            取消
                          </Button>
                        </Box>
                      </Box>
                    ) : (
                      <>
                        <ListItemText
                          primary={prompt.content}
                          secondary={
                            <Chip 
                              label={category?.name || '未分类'} 
                              size="small" 
                              variant="outlined" 
                            />
                          }
                        />
                        <ListItemSecondaryAction>
                          <IconButton size="small" onClick={() => handleEditPrompt(prompt)}>
                            <EditIcon />
                          </IconButton>
                          <IconButton size="small" onClick={() => handleDeletePrompt(prompt.id)}>
                            <DeleteIcon />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </>
                    )}
                  </ListItem>
                );
              })}
            </List>
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>关闭</Button>
      </DialogActions>
    </Dialog>
  );
};

export default TextPromptManager;
