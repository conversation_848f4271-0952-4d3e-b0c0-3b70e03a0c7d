import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Position, useReactFlow } from 'reactflow';
import {
  Paper,
  Typography,
  Box,
  TextField,
  IconButton,
  Tooltip,
  Button,
} from '@mui/material';
import { 
  Output as OutputIcon, 
  ContentCopy as CopyIcon,
  PlayArrow as PlayIcon 
} from '@mui/icons-material';
import type { OutputNodeData, NodeProps } from '../../types';

const OutputNode: React.FC<NodeProps<OutputNodeData>> = ({ data, selected, id }) => {
  const [finalPrompt, setFinalPrompt] = useState('');
  const { getNodes, getEdges } = useReactFlow();

  // 计算最终提示词
  useEffect(() => {
    const calculateFinalPrompt = () => {
      const nodes = getNodes();
      const edges = getEdges();
      
      // 找到所有连接到输出节点的边
      const connectedEdges = edges.filter(edge => edge.target === id);
      
      // 获取连接的源节点
      const connectedNodes = connectedEdges.map(edge => 
        nodes.find(node => node.id === edge.source)
      ).filter(Boolean);

      // 构建最终提示词
      let promptParts: string[] = [];

      connectedNodes.forEach(node => {
        if (!node) return;

        switch (node.type) {
          case 'textNode':
            if (node.data.content) {
              promptParts.push(node.data.content);
            }
            break;
          
          case 'imageNode':
            // 对于图片节点，可以添加图片相关的描述
            if (node.data.name) {
              promptParts.push(`[Image: ${node.data.name}]`);
            }
            break;
          
          case 'paramNode':
            if (node.data.paramValue !== undefined && node.data.paramValue !== null) {
              const paramString = `--${node.data.paramName.toLowerCase().replace(/\s+/g, '-')} ${node.data.paramValue}`;
              promptParts.push(paramString);
            }
            break;
        }
      });

      const newPrompt = promptParts.join(', ');
      setFinalPrompt(newPrompt);
    };

    calculateFinalPrompt();
  }, [getNodes, getEdges, id]);

  const handleCopyPrompt = async () => {
    try {
      await navigator.clipboard.writeText(finalPrompt);
      // TODO: 显示成功提示
    } catch (err) {
      console.error('复制失败:', err);
    }
  };

  const handleExecuteGeneration = () => {
    // TODO: 实现AI生成功能
    console.log('执行生成:', finalPrompt);
  };

  return (
    <Paper
      elevation={selected ? 8 : 4}
      sx={{
        padding: 2,
        minWidth: 350,
        maxWidth: 500,
        border: selected ? '2px solid #1976d2' : '2px solid #4caf50',
        borderRadius: 2,
        backgroundColor: '#f8fff8',
        cursor: 'pointer',
        transition: 'all 0.2s ease-in-out',
        '&:hover': {
          elevation: 6,
          transform: 'translateY(-1px)',
        },
      }}
    >
      {/* 输入端口 */}
      <Handle
        type="target"
        position={Position.Left}
        style={{
          background: '#4caf50',
          width: 12,
          height: 12,
          border: '2px solid #fff',
        }}
      />

      {/* 节点头部 */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <OutputIcon sx={{ color: '#4caf50', mr: 1, fontSize: 24 }} />
        <Typography variant="subtitle1" sx={{ flexGrow: 1, fontWeight: 'bold' }}>
          {data.label || '最终咒语集'}
        </Typography>
        <Tooltip title="复制提示词">
          <IconButton 
            size="small" 
            onClick={handleCopyPrompt}
            sx={{ mr: 1 }}
          >
            <CopyIcon sx={{ fontSize: 18 }} />
          </IconButton>
        </Tooltip>
      </Box>

      {/* 最终提示词显示 */}
      <TextField
        fullWidth
        multiline
        rows={6}
        value={finalPrompt}
        placeholder="连接其他节点以生成最终提示词..."
        InputProps={{
          readOnly: true,
          sx: {
            backgroundColor: '#fff',
            '& .MuiInputBase-input': {
              fontSize: '0.9rem',
              lineHeight: 1.4,
            },
          },
        }}
        sx={{ mb: 2 }}
      />

      {/* 操作按钮 */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="caption" color="text.secondary">
          字符数: {finalPrompt.length}
        </Typography>
        
        <Button
          variant="contained"
          startIcon={<PlayIcon />}
          onClick={handleExecuteGeneration}
          disabled={!finalPrompt.trim()}
          sx={{
            backgroundColor: '#4caf50',
            '&:hover': {
              backgroundColor: '#45a049',
            },
          }}
        >
          执行生成
        </Button>
      </Box>
    </Paper>
  );
};

export default OutputNode;
